on:
  push:
    tags: ["[0-9]+.[0-9]+.[0-9]+"]
    branches:
      - main

jobs:
  release:
    if: github.ref_type == 'tag' && contains(github.ref_name, '.')
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [macos-latest, ubuntu-latest, windows-latest]

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install corepack
        run: corepack enable && corepack prepare yarn@4.9.2 --activate

      - name: Install Dependencies
        run: yarn install
        env:
          NODE_OPTIONS: --max-old-space-size=8192

      - name: apt-update
        if: startsWith(matrix.os, 'ubuntu-latest')
        run: sudo apt-get update

      - name: autoremove
        if: startsWith(matrix.os, 'ubuntu-latest')
        run: sudo apt autoremove

      - name: Install libarchive rpm on Linux
        if: startsWith(matrix.os, 'ubuntu-latest')
        run: sudo apt-get install libarchive-tools rpm

      - name: Release Electron app
        uses: DarkGuy10/action-electron-builder@v1.0.0
        with:
          package_manager: "yarn"
          # GitHub token, automatically provided to the action
          # (No need to define this secret in the repo settings)
          github_token: ${{ secrets.github_token }}

          # Mac certs
          mac_certs: ${{ secrets.mac_certs }}
          mac_certs_password: ${{ secrets.mac_certs_password }}

          # If the commit is tagged with a version (e.g. "25.26.0"),
          # release the app after building
          release: true
          # build_script_name: "prebuild"
          max_attempts: 3
        env:
          NODE_OPTIONS: --max-old-space-size=8192
          # macOS notarization API key
          APPLE_ID: ${{ secrets.APPLE_ID }}
          APPLE_APP_SPECIFIC_PASSWORD: ${{ secrets.APPLE_APP_SPECIFIC_PASSWORD }}
          APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
