{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["src/**", "!.vite/**"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2}, "linter": {"enabled": true, "rules": {"recommended": true, "nursery": {"useSortedClasses": {"level": "error", "fix": "safe", "options": {"attributes": ["className"], "functions": ["cn"]}}}}}, "javascript": {"formatter": {"quoteStyle": "double"}, "parser": {"unsafeParameterDecoratorsEnabled": true}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}