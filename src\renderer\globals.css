/*
* Uncomment the following line to use the Inter font when not working on a Next.js project.
* @import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
* Then replace var(--font-inter) to Inter
*/
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@import "tailwindcss";
@import "tw-animate-css";
@plugin 'tailwindcss-react-aria-components';

@theme {
  --font-sans: "Inter", ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-mono: "Geist Mono", "ui-monospace", "SFMono-Regular", "Menlo", "Monaco",
    "Consolas", '"Liberation Mono"', '"Courier New"', "monospace";

  --color-border: var(--border);
  --color-input: var(--input);

  --color-ring: var(--ring);

  --color-bg: var(--bg);
  --color-fg: var(--fg);

  --color-primary: var(--primary);
  --color-primary-fg: var(--primary-fg);

  --color-secondary: var(--secondary);
  --color-secondary-fg: var(--secondary-fg);

  --color-accent: var(--accent);
  --color-accent-fg: var(--accent-fg);
  --color-accent-hover: var(--accent-hover);

  --color-success: var(--success);
  --color-success-fg: var(--success-fg);
  --color-success-bg: var(--success-bg);
  --color-success-border: var(--success-border);
  --color-success-text: var(--success-text);

  --color-info-bg: var(--info-bg);
  --color-info-border: var(--info-border);
  --color-info-text: var(--info-text);

  --color-danger: var(--danger);
  --color-danger-2: var(--danger-2);
  --color-danger-fg: var(--danger-fg);
  --color-danger-fg-2: var(--danger-fg-2);

  --color-destructive: var(--destructive);
  --color-destructive-fg: var(--destructive-fg);

  --color-warning: var(--warning);
  --color-warning-fg: var(--warning-fg);
  --color-warning-bg: var(--warning-bg);
  --color-warning-border: var(--warning-border);
  --color-warning-text: var(--warning-text);

  --color-error-bg: var(--error-bg);
  --color-error-border: var(--error-border);
  --color-error-text: var(--error-text);

  --color-muted: var(--muted);
  --color-muted-fg: var(--muted-fg);

  --color-selected: var(--selected);
  --color-selected-fg: var(--selected-fg);

  --color-hover: var(--hover);
  --color-hover-fg: var(--hover-fg);

  --color-hover-2: var(--hover-2);

  --color-overlay: var(--overlay);
  --color-overlay-fg: var(--overlay-fg);

  --color-hover-primary: var(--hover-primary);
  --color-hover-primary-fg: var(--hover-primary-fg);
  --color-hover-secondary: var(--hover-secondary);
  --color-hover-secondary-fg: var(--hover-secondary-fg);

  --color-label-fg: var(--label-fg);

  --color-btn-overlay: var(--btn-overlay);

  --color-navbar: var(--navbar);
  --color-navbar-fg: var(--navbar-fg);

  --color-sidebar: var(--sidebar);
  --color-sidebar-fg: var(--sidebar-fg);

  --color-setting: var(--setting);
  --color-setting-fg: var(--setting-fg);

  --color-card-desc-text: var(--card-desc-text);
  --color-card-title-text: var(--card-title-text);
  --color-card-desc-error:var(--card-desc-error);

  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
}

@layer base {
  :root {
    --bg: #FFFFFF;
    --fg: #333333;

    --primary: #8E47F0;
    --primary-fg: #FFFFFF;

    --secondary: oklch(0.923 0.003 48.717);
    --secondary-fg: rgba(102, 102, 102, 1);

    --overlay: #F9F9F9;
    --overlay-fg: #333333;

    --accent: #F3F2FF;
    --accent-fg: #8450E8;
    --accent-hover: #e6d9f7;

    --muted: #F1F1F1;
    --muted-fg: #AAAAAA;

    --hover: #F1F1F1;
    --hover-fg: #000000;
    --hover-2: #F9F9F9;

    --success: oklch(0.596 0.145 163.225);
    --success-fg: oklch(1 0 0);
    --success-bg: #ecfdf5;
    --success-border: #d1fae5;
    --success-text: #059669;

    --info-bg: rgba(37, 99, 235, 0.15);
    --info-border: rgba(37, 99, 235, 0.15);
    --info-text: #2563eb;

    --warning: oklch(0.828 0.189 84.429);
    --warning-fg: oklch(0.279 0.077 45.635);
    --warning-bg: rgba(245, 158, 11, 0.15);
    --warning-border: rgba(251, 191, 36, 0.4);
    --warning-text: #d97706;

    --error-bg: rgba(239, 68, 68, 0.2);
    --error-border: #fecaca;
    --error-text: #dc2626;

    --danger: oklch(0.577 0.245 27.325);
    --danger-2: #FFE7E7;
    --danger-fg: oklch(0.971 0.013 17.38);
    --danger-fg-2: #D82525;

    --destructive: oklch(0.67 0.24 25.76);
    --destructive-fg: oklch(0.98 0 0);

    --border: rgba(229, 229, 229, 1);
    --input: #F9F9F9;
    --ring: #8E47F0;

    --hover-primary: rgba(241, 241, 241, 1);
    --hover-primary-fg: rgba(51, 51, 51, 1);
    --hover-secondary: #f9f9f9;
    --hover-secondary-fg: rgba(102, 102, 102, 1);

    --label-fg: #8E8E8E;

    --navbar: #F9F9F9;
    --navbar-fg:  #333333;

    --sidebar: #F9F9F9;
    --sidebar-fg: #333333;

    --setting: #F9F9F9;
    --setting-fg: #333333;

    --card-desc-text: #AAAAAA;
    --card-title-text: #333333;
    --card-desc-error:#D82525;

    --chart-1: oklch(0.541 0.281 293.009);
    --chart-2: oklch(0.702 0.183 293.541);
    --chart-3: oklch(0.811 0.111 293.571);
    --chart-4: oklch(0.894 0.057 293.283);
    --chart-5: oklch(0.943 0.029 294.588);

    --radius-lg: 0.5rem;
    --radius-xs: calc(var(--radius-lg) * 0.5);
    --radius-sm: calc(var(--radius-lg) * 0.75);
    --radius-md: calc(var(--radius-lg) * 0.9);
    --radius-xl: calc(var(--radius-lg) * 1.25);
    --radius-2xl: calc(var(--radius-lg) * 1.5);
    --radius-3xl: calc(var(--radius-lg) * 2);
    --radius-4xl: calc(var(--radius-lg) * 3);
  }

  .dark {
    --bg: #121212;
    --fg: #E6E6E6;

    --primary: #8E47F0;
    --primary-fg: #FFFFFF;

    --secondary: #2D2D2D;
    --secondary-fg: #E6E6E6;

    --accent: #49306A;
    --accent-fg: #E6E6E6;
    --accent-hover: #503a6c;

    --muted: #2D2D2D;
    --muted-fg: #5C5C5C;

    --hover: #2D2D2D;
    --hover-fg: #E6E6E6;
    --hover-2: #383838;

    --overlay: #1A1A1A;
    --overlay-fg: #E6E6E6;

    --success: oklch(0.596 0.145 163.225);
    --success-fg: oklch(1 0 0);
    --success-bg: oklch(26.2% 0.051 172.552);
    --success-border: oklch(37.8% 0.077 168.94);
    --success-text: oklch(90.5% 0.093 164.15);

    --info-bg: rgba(37, 99, 235, 0.15);
    --info-border: rgba(37, 99, 235, 0.15);
    --info-text: #bfdbfe;

    --warning: oklch(0.828 0.189 84.429);
    --warning-fg: oklch(0.279 0.077 45.635);
    --warning-bg: rgba(251, 191, 36, 0.1);
    --warning-border: rgba(245, 158, 11, 0.15);
    --warning-text: #fde68a;

    --error-bg: rgba(220, 38, 38, 0.1);
    --error-border: rgba(220, 38, 38, 0.15);
    --error-text: #fca5a5;

    --danger: oklch(0.577 0.245 27.325);
    --danger-2: #3A1A1A;
    --danger-fg: oklch(0.971 0.013 17.38);
    --danger-fg-2: #FF4D4D;

    --destructive: oklch(0.61 0.25 25.9);
    --destructive-fg: oklch(0.98 0 0);

    --border: oklch(0.274 0.01 67.558);
    --input: #1A1A1A;
    --ring: #8E47F0;

    --hover-primary: hsl(240, 4%, 16%);
    --hover-primary-fg: oklch(0.985 0.001 106.423);
    --hover-secondary:#383838;
    --hover-secondary-fg: oklch(0.985 0.001 106.423);

    --label-fg: #A0A0A0;

    --navbar: #1A1A1A;
    --navbar-fg: #E6E6E6;

    --sidebar: #1A1A1A;
    --sidebar-fg: #E6E6E6;

    --setting: #1A1A1A;
    --setting-fg: #E6E6E6;

    --card-desc-text: #7A7A7A;
    --card-title-text: #E6E6E6;
    --card-desc-error:#D82525;

    --chart-1: oklch(0.491 0.27 292.581);
    --chart-2: oklch(0.606 0.25 292.717);
    --chart-3: oklch(0.702 0.183 293.541);
    --chart-4: oklch(0.811 0.111 293.571);
    --chart-5: oklch(0.894 0.057 293.283);
  }
}

@variant dark (&:is(.dark *));

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--border, currentColor);
  }

  * {
    text-rendering: optimizeLegibility;
    scrollbar-width: thin;
    scrollbar-color: var(--border) transparent;
  }

  * {
    --title-bar-height: 46px;
    --sidebar-width: 280px;
    --sidebar-width-dock: 95px;
    --sidebar-width-collapsed: 95px;
    --setting-width: 170px;
    --chat-input-toolbar-height: 36px;
  }

  html {
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-variation-settings: normal;
    scroll-behavior: smooth;
    height: 100vh;
    overflow: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-tap-highlight-color: transparent;
  }


  body {
    background-color: var(--bg);
    color: var(--fg);
  }

  ::-webkit-scrollbar {
    width: 4px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 4px;
  }
}

.katex {
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  font-feature-settings: "kern" 1 !important;
}

.katex-display {
  overflow-x: auto;
  overflow-y: hidden;
  padding: 0.5em 0;
  margin: 0.5em 0;
}

.katex .frac-line {
  border-bottom-width: 0.04em !important;
}
