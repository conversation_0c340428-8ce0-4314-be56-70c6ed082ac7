<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="initial-scale=1, width=device-width" />
  <meta http-equiv="Content-Security-Policy"
    content="default-src 'self'; connect-src blob: *; script-src 'self' 'unsafe-eval' *; worker-src 'self' blob:; style-src 'self' 'unsafe-inline' *; font-src 'self' data: *; img-src 'self' data: file: * blob:; media-src 'self' data: blob: file:; frame-src * file:" />
</head>

<body>
  <app></app>
  <script type="module" src="main.tsx"></script>
</body>

</html>