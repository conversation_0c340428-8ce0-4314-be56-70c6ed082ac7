import type { registerRoute } from "@lib/electron-router-dom";
import type { <PERSON><PERSON>erWindow, IpcMainInvokeEvent } from "electron";

export type BrowserWindowOrNull = Electron.BrowserWindow | null;

type Route = Parameters<typeof registerRoute>[0];

export interface WindowProps extends Electron.BrowserWindowConstructorOptions {
  id: Route["id"];
  query?: Route["query"];
}

export interface WindowCreationByIPC {
  channel: string;
  window(): BrowserWindowOrNull;
  callback(window: BrowserWindow, event: IpcMainInvokeEvent): void;
}
