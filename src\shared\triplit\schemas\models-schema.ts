import { Schema as S } from "@triplit/client";

export const modelsSchema = {
  schema: S.Schema({
    id: S.Id({ format: "nanoid" }),
    name: S.String(),
    remark: S.String({ default: "" }),
    providerId: S.String(),
    capabilities: S.Set(S.String()),
    type: S.String({
      enum: ["language", "image-generation", "tts", "embedding", "rerank"],
      default: "language",
    }),
    custom: <PERSON><PERSON>({ default: false }),
    enabled: <PERSON><PERSON>({ default: true }),
    collected: <PERSON><PERSON>({ default: false }),
  }),
  relationships: {
    provider: S.RelationById("providers", "$providerId"),
  },
};
