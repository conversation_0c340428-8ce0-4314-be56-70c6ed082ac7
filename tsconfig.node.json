{"extends": "@electron-toolkit/tsconfig/tsconfig.node.json", "include": ["electron.vite.config.*", "src/main/**/*", "src/lib/**/*", "src/resources/**/*", "src/preload/**/*", "src/shared/**/*", "src/renderer/client.ts"], "compilerOptions": {"composite": true, "types": ["electron-vite/node"], "moduleResolution": "bundler", "experimentalDecorators": true, "emitDecoratorMetadata": true, "target": "ESNext", "useDefineForClassFields": false, "baseUrl": ".", "paths": {"@lib/*": ["src/lib/*"], "@main/*": ["src/main/*"], "@shared/*": ["src/shared/*"]}}}