{"extends": "@electron-toolkit/tsconfig/tsconfig.web.json", "include": ["src/renderer/**/*", "src/preload/*.d.ts", "src/shared/**/*", "src/lib/**/*", "src/types/**/*"], "compilerOptions": {"composite": true, "jsx": "react-jsx", "resolveJsonModule": true, "moduleResolution": "bundler", "baseUrl": ".", "paths": {"@renderer/*": ["src/renderer/*"], "@shared/*": ["src/shared/*"], "@lib/*": ["src/lib/*"]}}}